package main

import (
	"blacking-api/internal/config"
	"blacking-api/migrations/list"
	"blacking-api/migrations/seeders"
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/lib/pq"
)

// Migratable interface for migrations
type Migratable interface {
	GetName() string
	Up(con pgx.Tx)
	Down(con pgx.Tx)
}

// ensureDatabaseExists checks if the database exists and creates it if it doesn't
func ensureDatabaseExists(cfg *config.Config) error {
	// Parse DSN to extract database name
	dsn := cfg.GetDatabaseDSN()

	// Connect to postgres without database name to check if target database exists
	dsnWithoutDB := strings.Replace(dsn, "/"+cfg.Database.DBName, "/postgres", 1)

	log.Printf("Checking if database %s exists", cfg.Database.DBName)

	db, err := sql.Open("postgres", dsnWithoutDB)
	if err != nil {
		return fmt.Errorf("failed to connect to PostgreSQL server: %v", err)
	}
	defer db.Close()

	// Set connection timeout
	db.SetConnMaxLifetime(time.Second * 10)

	// Check connection
	err = db.Ping()
	if err != nil {
		return fmt.Errorf("failed to ping PostgreSQL server: %v", err)
	}

	log.Printf("Successfully connected to PostgreSQL server")

	// Check if database exists
	var exists bool
	query := "SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = $1)"
	err = db.QueryRow(query, cfg.Database.DBName).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %v", err)
	}

	if !exists {
		log.Printf("Database %s does not exist, creating it now", cfg.Database.DBName)

		// Create database
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE \"%s\"", cfg.Database.DBName))
		if err != nil {
			return fmt.Errorf("failed to create database: %v", err)
		}

		log.Printf("Database %s created successfully", cfg.Database.DBName)
	} else {
		log.Printf("Database %s already exists", cfg.Database.DBName)
	}

	return nil
}

// createMigrationsTable creates the migrations tracking table if it doesn't exist
func createMigrationsTable(pool *pgxpool.Pool, cfg *config.Config) error {
	// Get schema name from config
	schemaName := cfg.Database.Schema
	if schemaName == "" {
		schemaName = "public"
	}

	// Create schema first if not public
	ctx := context.Background()
	if schemaName != "public" {
		_, err := pool.Exec(ctx, fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName))
		if err != nil {
			return fmt.Errorf("failed to create schema %s: %v", schemaName, err)
		}
	}

	// Create migrations table in the specified schema
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s.migrations (
			id SERIAL PRIMARY KEY,
			migration_name VARCHAR(255) NOT NULL UNIQUE,
			executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`, schemaName)

	_, err := pool.Exec(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to create migrations table: %v", err)
	}

	log.Printf("Migration tracking table created in schema: %s", schemaName)
	return nil
}

// isMigrationExecuted checks if a migration has already been executed
func isMigrationExecuted(pool *pgxpool.Pool, migrationName string, cfg *config.Config) (bool, error) {
	schemaName := cfg.Database.Schema
	if schemaName == "" {
		schemaName = "public"
	}

	var count int
	ctx := context.Background()
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s.migrations WHERE migration_name = $1", schemaName)
	err := pool.QueryRow(ctx, query, migrationName).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check migration status: %v", err)
	}
	return count > 0, nil
}

// recordMigration records that a migration has been executed
func recordMigration(pool *pgxpool.Pool, migrationName string, cfg *config.Config) error {
	schemaName := cfg.Database.Schema
	if schemaName == "" {
		schemaName = "public"
	}

	ctx := context.Background()
	query := fmt.Sprintf("INSERT INTO %s.migrations (migration_name) VALUES ($1)", schemaName)
	_, err := pool.Exec(ctx, query, migrationName)
	if err != nil {
		return fmt.Errorf("failed to record migration: %v", err)
	}
	return nil
}

// removeMigrationRecord removes a migration record from the tracking table
func removeMigrationRecord(pool *pgxpool.Pool, migrationName string, cfg *config.Config) error {
	schemaName := cfg.Database.Schema
	if schemaName == "" {
		schemaName = "public"
	}

	ctx := context.Background()
	query := fmt.Sprintf("DELETE FROM %s.migrations WHERE migration_name = $1", schemaName)
	_, err := pool.Exec(ctx, query, migrationName)
	if err != nil {
		return fmt.Errorf("failed to remove migration record: %v", err)
	}
	return nil
}

// getExecutedMigrations returns a list of executed migrations in order
func getExecutedMigrations(pool *pgxpool.Pool, cfg *config.Config) ([]string, error) {
	schemaName := cfg.Database.Schema
	if schemaName == "" {
		schemaName = "public"
	}

	ctx := context.Background()
	query := fmt.Sprintf("SELECT migration_name FROM %s.migrations ORDER BY executed_at DESC", schemaName)
	rows, err := pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get executed migrations: %v", err)
	}
	defer rows.Close()

	var migrations []string
	for rows.Next() {
		var migrationName string
		err := rows.Scan(&migrationName)
		if err != nil {
			return nil, fmt.Errorf("failed to scan migration name: %v", err)
		}
		migrations = append(migrations, migrationName)
	}

	return migrations, nil
}

func main() {
	// Parse command line arguments
	if len(os.Args) < 2 {
		log.Fatalf("Usage: %s [environment] [command] [args...]", os.Args[0])
	}

	_ = os.Args[1]  // environment (not used in current implementation)
	command := "up" // default command
	var args []string

	if len(os.Args) > 2 {
		command = os.Args[2]
		if len(os.Args) > 3 {
			args = os.Args[3:]
		}
	}

	log.Printf("Running migrations with command: %s", command)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Ensure database exists before running migrations
	err = ensureDatabaseExists(cfg)
	if err != nil {
		log.Fatalf("Failed to ensure database exists: %v", err)
	}

	// Connect to database with native pgx
	dsn := cfg.GetDatabaseDSN()

	// Apply DigitalOcean optimizations if needed
	if strings.Contains(cfg.Database.Host, ".ondigitalocean.com") {
		dsn += " default_query_exec_mode=simple_protocol statement_cache_capacity=0"
		log.Printf("Applied DigitalOcean pgx optimizations")
	}

	log.Printf("Connecting to database with pgx: %s", dsn)

	ctx := context.Background()
	pool, err := pgxpool.New(ctx, dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer pool.Close()

	log.Printf("Successfully connected to database")

	// Create migrations tracking table
	err = createMigrationsTable(pool, cfg)
	if err != nil {
		log.Fatalf("Failed to create migrations table: %v", err)
	}

	// Handle different commands
	switch command {
	case "up":
		runMigrationsUp(pool, cfg)
	case "down":
		steps := 1 // default rollback 1 migration
		if len(args) > 0 {
			if s, err := strconv.Atoi(args[0]); err == nil && s > 0 {
				steps = s
			}
		}
		runMigrationsDown(pool, cfg, steps)
	case "seed":
		runSeeders(pool, cfg)
	case "status":
		showMigrationStatus(pool, cfg)
	case "validate":
		validateMigrations()
	case "dry-run":
		dryRunMigrations(pool, cfg)
	default:
		log.Fatalf("Unknown command: %s. Available commands: up, down, seed, status, validate, dry-run", command)
	}
}

func runMigrationsUp(pool *pgxpool.Pool, cfg *config.Config) {
	// Get migrations list
	migrations := getMigrationsList()
	log.Printf("Found %d migrations", len(migrations))

	// Execute each migration if not already executed
	executedCount := 0
	skippedCount := 0
	ctx := context.Background()

	for i, migration := range migrations {
		migrationName := migration.GetName()

		// Check if migration has already been executed
		executed, err := isMigrationExecuted(pool, migrationName, cfg)
		if err != nil {
			log.Fatalf("Failed to check migration status for %s: %v", migrationName, err)
		}

		if executed {
			log.Printf("Skipping migration %d: %s (already executed)", i+1, migrationName)
			skippedCount++
			continue
		}

		log.Printf("Running migration %d: %s", i+1, migrationName)

		// Execute migration in a transaction
		tx, err := pool.Begin(ctx)
		if err != nil {
			log.Fatalf("Failed to begin transaction for migration %s: %v", migrationName, err)
		}

		// Run the migration
		func() {
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback(ctx)
					log.Fatalf("Migration %s panicked: %v", migrationName, r)
				}
			}()
			migration.Up(tx)
		}()

		// Record the migration as executed in the correct schema
		schemaName := cfg.Database.Schema
		if schemaName == "" {
			schemaName = "public"
		}
		recordQuery := fmt.Sprintf("INSERT INTO %s.migrations (migration_name) VALUES ($1)", schemaName)
		_, err = tx.Exec(ctx, recordQuery, migrationName)
		if err != nil {
			tx.Rollback(ctx)
			log.Fatalf("Failed to record migration %s: %v", migrationName, err)
		}

		err = tx.Commit(ctx)
		if err != nil {
			log.Fatalf("Failed to commit transaction for migration %s: %v", migrationName, err)
		}

		log.Printf("Completed migration %d: %s", i+1, migrationName)
		executedCount++
	}

	log.Printf("Migration summary: %d executed, %d skipped, %d total", executedCount, skippedCount, len(migrations))
}

func getMigrationsList() []Migratable {
	return []Migratable{
		&list.CreateInitTables{},
		&list.CreateBankingSystem{},
		&list.CreateFAQSystem{},
		&list.CreateMemberSystem{},
		&list.CreateContactSMSSystem{},
		&list.AddUserRoleNameTrigger{},
		&list.AddMemberRemark{},
		&list.AddIsTabAndIsButtonToPermissions{},
		&list.RemovePermissionGroups{},
		&list.CreateUserTransactionType{},
		&list.CreateUserTransactionDirection{},
		&list.CreateUserTransactionStatus{},
		&list.CreateThemeSettingTable{},
		&list.CreatePromotions{},
	}
}

func getSeedersList() []Migratable {
	return []Migratable{
		&seeders.SeedDefaultData{},
		&seeders.SeedPermissions{},
	}
}

func runSeeders(pool *pgxpool.Pool, cfg *config.Config) {
	seeders := getSeedersList()
	log.Printf("Found %d seeders", len(seeders))

	executedCount := 0
	skippedCount := 0

	for i, seeder := range seeders {
		seederName := seeder.GetName()

		// Check if seeder has already been executed (using same migrations table)
		executed, err := isMigrationExecuted(pool, seederName, cfg)
		if err != nil {
			log.Fatalf("Failed to check seeder status for %s: %v", seederName, err)
		}

		if executed {
			log.Printf("Skipping seeder %d: %s (already executed)", i+1, seederName)
			skippedCount++
			continue
		}

		log.Printf("Running seeder %d: %s", i+1, seederName)

		// Execute seeder in a transaction
		ctx := context.Background()
		tx, err := pool.Begin(ctx)
		if err != nil {
			log.Fatalf("Failed to begin transaction for seeder %s: %v", seederName, err)
		}

		// Run the seeder
		func() {
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback(ctx)
					log.Fatalf("Seeder %s panicked: %v", seederName, r)
				}
			}()
			seeder.Up(tx)
		}()

		// Record the seeder as executed in migrations table
		schemaName := cfg.Database.Schema
		if schemaName == "" {
			schemaName = "public"
		}
		recordQuery := fmt.Sprintf("INSERT INTO %s.migrations (migration_name) VALUES ($1)", schemaName)
		_, err = tx.Exec(ctx, recordQuery, seederName)
		if err != nil {
			tx.Rollback(ctx)
			log.Fatalf("Failed to record seeder %s: %v", seederName, err)
		}

		err = tx.Commit(ctx)
		if err != nil {
			log.Fatalf("Failed to commit transaction for seeder %s: %v", seederName, err)
		}

		log.Printf("Completed seeder %d: %s", i+1, seederName)
		executedCount++
	}

	log.Printf("Seeder summary: %d executed, %d skipped, %d total", executedCount, skippedCount, len(seeders))
}

// runMigrationsDown rolls back the specified number of migrations
func runMigrationsDown(pool *pgxpool.Pool, cfg *config.Config, steps int) {
	log.Printf("Rolling back %d migration(s)...", steps)

	// Get executed migrations in reverse order (most recent first)
	executedMigrations, err := getExecutedMigrations(pool, cfg)
	if err != nil {
		log.Fatalf("Failed to get executed migrations: %v", err)
	}

	if len(executedMigrations) == 0 {
		log.Printf("No migrations to rollback")
		return
	}

	// Get all available migrations
	allMigrations := getMigrationsList()
	migrationMap := make(map[string]Migratable)
	for _, migration := range allMigrations {
		migrationMap[migration.GetName()] = migration
	}

	// Get all available seeders
	allSeeders := getSeedersList()
	seederMap := make(map[string]Migratable)
	for _, seeder := range allSeeders {
		seederMap[seeder.GetName()] = seeder
	}

	ctx := context.Background()
	rolledBackCount := 0
	skippedCount := 0

	// Rollback the specified number of migrations (including seeders)
	for i := 0; i < len(executedMigrations) && rolledBackCount < steps; i++ {
		migrationName := executedMigrations[i]

		var migration Migratable
		var exists bool
		var itemType string

		// Check if this is a seeder first
		migration, exists = seederMap[migrationName]
		if exists {
			itemType = "seeder"
		} else {
			// Check if this is a migration
			migration, exists = migrationMap[migrationName]
			if exists {
				itemType = "migration"
			}
		}

		if !exists {
			log.Printf("Warning: %s not found in available migrations or seeders, skipping rollback", migrationName)
			skippedCount++
			continue
		}

		log.Printf("Rolling back %s: %s", itemType, migrationName)

		log.Printf("Rolling back migration: %s", migrationName)

		// Execute rollback in a transaction
		tx, err := pool.Begin(ctx)
		if err != nil {
			log.Fatalf("Failed to begin transaction for rollback %s: %v", migrationName, err)
		}

		// Run the down migration
		func() {
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback(ctx)
					log.Fatalf("Migration rollback %s panicked: %v", migrationName, r)
				}
			}()
			migration.Down(tx)
		}()

		// Remove the migration record within the transaction
		schemaName := cfg.Database.Schema
		if schemaName == "" {
			schemaName = "public"
		}
		removeQuery := fmt.Sprintf("DELETE FROM %s.migrations WHERE migration_name = $1", schemaName)
		_, err = tx.Exec(ctx, removeQuery, migrationName)
		if err != nil {
			tx.Rollback(ctx)
			log.Fatalf("Failed to remove migration record %s: %v", migrationName, err)
		}

		err = tx.Commit(ctx)
		if err != nil {
			log.Fatalf("Failed to commit rollback transaction for migration %s: %v", migrationName, err)
		}

		log.Printf("Successfully rolled back migration: %s", migrationName)
		rolledBackCount++
	}

	log.Printf("Rollback summary: %d migrations rolled back, %d items skipped (seeders/not found)", rolledBackCount, skippedCount)
}

// showMigrationStatus shows the current migration status
func showMigrationStatus(pool *pgxpool.Pool, cfg *config.Config) {
	log.Printf("Migration Status:")
	log.Printf("================")

	// Get all available migrations
	allMigrations := getMigrationsList()

	// Get executed migrations
	executedMigrations, err := getExecutedMigrations(pool, cfg)
	if err != nil {
		log.Fatalf("Failed to get executed migrations: %v", err)
	}

	// Create a map for quick lookup
	executedMap := make(map[string]bool)
	for _, name := range executedMigrations {
		executedMap[name] = true
	}

	// Show status for each migration
	for i, migration := range allMigrations {
		migrationName := migration.GetName()
		status := "PENDING"
		if executedMap[migrationName] {
			status = "EXECUTED"
		}
		log.Printf("%d. %s - %s", i+1, migrationName, status)
	}

	log.Printf("\nTotal migrations: %d", len(allMigrations))
	log.Printf("Executed: %d", len(executedMigrations))
	log.Printf("Pending: %d", len(allMigrations)-len(executedMigrations))
}

// validateMigrations validates all migration files
func validateMigrations() {
	log.Printf("Validating migrations...")

	migrations := getMigrationsList()
	log.Printf("Found %d migrations to validate", len(migrations))

	for i, migration := range migrations {
		migrationName := migration.GetName()
		log.Printf("Validating migration %d: %s", i+1, migrationName)

		// Basic validation - check if migration has required methods
		if migrationName == "" {
			log.Fatalf("Migration %d has empty name", i+1)
		}

		// Additional validation can be added here
		log.Printf("✓ Migration %s is valid", migrationName)
	}

	log.Printf("All migrations are valid!")
}

// dryRunMigrations shows what migrations would be executed without running them
func dryRunMigrations(pool *pgxpool.Pool, cfg *config.Config) {
	log.Printf("Dry run - showing what migrations would be executed:")
	log.Printf("=================================================")

	// Get migrations list
	migrations := getMigrationsList()

	pendingCount := 0

	for i, migration := range migrations {
		migrationName := migration.GetName()

		// Check if migration has already been executed
		executed, err := isMigrationExecuted(pool, migrationName, cfg)
		if err != nil {
			log.Fatalf("Failed to check migration status for %s: %v", migrationName, err)
		}

		if executed {
			log.Printf("SKIP: %d. %s (already executed)", i+1, migrationName)
		} else {
			log.Printf("RUN:  %d. %s", i+1, migrationName)
			pendingCount++
		}
	}

	log.Printf("\nDry run summary:")
	log.Printf("Total migrations: %d", len(migrations))
	log.Printf("Would execute: %d", pendingCount)
	log.Printf("Would skip: %d", len(migrations)-pendingCount)
}
